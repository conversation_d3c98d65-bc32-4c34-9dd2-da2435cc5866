import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';
import { RecipeSource } from '@/components/types';
import { RECIPE_SOURCES } from '@/constants/RecipeConstants';

interface RecipeSourceTabsProps {
  selectedSource: RecipeSource;
  onSourceChange: (source: RecipeSource) => void;
}

const RecipeSourceTabs: React.FC<RecipeSourceTabsProps> = ({
  selectedSource,
  onSourceChange,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');

  return (
    <View
      style={{
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingVertical: 8,
        backgroundColor: colors.background,
        borderBottomWidth: 1,
        borderBottomColor: colors.border || '#E0E0E0',
      }}
    >
      {RECIPE_SOURCES.map((source) => (
        <TouchableOpacity
          key={source}
          style={{
            flex: 1,
            alignItems: 'center',
            paddingVertical: 12,
            paddingHorizontal: 8,
          }}
          onPress={() => onSourceChange(source)}
        >
          <Text
            style={{
              fontSize: 16,
              fontWeight: selectedSource === source ? 'bold' : '500',
              color: selectedSource === source ? colors.accent : colors.text,
              marginBottom: 4,
            }}
          >
            {source}
          </Text>
          {selectedSource === source && (
            <View
              style={{
                height: 3,
                width: '80%',
                backgroundColor: colors.accent,
                borderRadius: 1.5,
              }}
            />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default RecipeSourceTabs;
